'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Customer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Customer has many SalesOrders
      Customer.hasMany(models.SalesOrder, {
        foreignKey: 'customer_id',
        as: 'salesOrders',
        onDelete: 'CASCADE'
      });
    }
  }

  Customer.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    company_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Company name is required'
        },
        len: {
          args: [1, 200],
          msg: 'Company name must be between 1 and 200 characters'
        }
      }
    },
    contact_info: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      validate: {
        isValidContactInfo(value) {
          if (value && typeof value !== 'object') {
            throw new Error('Contact info must be a valid JSON object');
          }
          if (value && value.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.email)) {
            throw new Error('Invalid email format in contact info');
          }
        }
      }
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        isDecimal: {
          msg: 'Credit limit must be a valid decimal number'
        },
        min: {
          args: [0],
          msg: 'Credit limit must be non-negative'
        }
      }
    },
    pricing_tier: {
      type: DataTypes.ENUM('standard', 'premium', 'wholesale'),
      allowNull: false,
      defaultValue: 'standard',
      validate: {
        isIn: {
          args: [['standard', 'premium', 'wholesale']],
          msg: 'Pricing tier must be one of: standard, premium, wholesale'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Customer',
    tableName: 'customers',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['company_name']
      },
      {
        fields: ['pricing_tier']
      },
      {
        fields: ['credit_limit']
      }
    ]
  });

  return Customer;
};
