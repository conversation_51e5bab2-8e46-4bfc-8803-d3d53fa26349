'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class InventoryItem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // InventoryItem belongs to Product
      InventoryItem.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product',
        onDelete: 'RESTRICT'
      });

      // InventoryItem belongs to Container
      InventoryItem.belongsTo(models.Container, {
        foreignKey: 'container_id',
        as: 'container',
        onDelete: 'CASCADE'
      });

      // InventoryItem has one OrderItem
      InventoryItem.hasOne(models.OrderItem, {
        foreignKey: 'inventory_item_id',
        as: 'orderItem',
        onDelete: 'RESTRICT'
      });
    }
  }

  InventoryItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Product ID is required'
        },
        isInt: {
          msg: 'Product ID must be an integer'
        }
      }
    },
    container_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'containers',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Container ID is required'
        },
        isInt: {
          msg: 'Container ID must be an integer'
        }
      }
    },
    serial_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        name: 'serial_number_unique',
        msg: 'Serial number must be unique'
      },
      validate: {
        notEmpty: {
          msg: 'Serial number is required'
        },
        len: {
          args: [1, 100],
          msg: 'Serial number must be between 1 and 100 characters'
        }
      }
    },
    condition: {
      type: DataTypes.ENUM('new', 'refurbished', 'damaged'),
      allowNull: false,
      defaultValue: 'new',
      validate: {
        isIn: {
          args: [['new', 'refurbished', 'damaged']],
          msg: 'Condition must be one of: new, refurbished, damaged'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('available', 'reserved', 'sold'),
      allowNull: false,
      defaultValue: 'available',
      validate: {
        isIn: {
          args: [['available', 'reserved', 'sold']],
          msg: 'Status must be one of: available, reserved, sold'
        }
      }
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: 'Cost price must be a valid decimal number'
        },
        min: {
          args: [0.01],
          msg: 'Cost price must be positive'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'InventoryItem',
    tableName: 'inventory_items',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['serial_number']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['container_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['condition']
      }
    ]
  });

  return InventoryItem;
};
