'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SalesOrder extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // SalesOrder belongs to Customer
      SalesOrder.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer',
        onDelete: 'CASCADE'
      });

      // SalesOrder has many OrderItems
      SalesOrder.hasMany(models.OrderItem, {
        foreignKey: 'order_id',
        as: 'orderItems',
        onDelete: 'CASCADE'
      });
    }
  }

  SalesOrder.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Customer ID is required'
        },
        isInt: {
          msg: 'Customer ID must be an integer'
        }
      }
    },
    order_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      validate: {
        isDate: {
          msg: 'Order date must be a valid date'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
      validate: {
        isIn: {
          args: [['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']],
          msg: 'Status must be one of: pending, confirmed, shipped, delivered, cancelled'
        }
      }
    },
    total_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        isDecimal: {
          msg: 'Total amount must be a valid decimal number'
        },
        min: {
          args: [0],
          msg: 'Total amount must be non-negative'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'SalesOrder',
    tableName: 'sales_orders',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['customer_id']
      },
      {
        fields: ['order_date']
      },
      {
        fields: ['status']
      },
      {
        fields: ['total_amount']
      }
    ]
  });

  return SalesOrder;
};
