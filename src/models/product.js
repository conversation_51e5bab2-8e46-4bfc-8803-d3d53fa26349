'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Product has many InventoryItems
      Product.hasMany(models.InventoryItem, {
        foreignKey: 'product_id',
        as: 'inventoryItems',
        onDelete: 'RESTRICT'
      });
    }
  }

  Product.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    brand: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Brand is required'
        },
        len: {
          args: [1, 100],
          msg: 'Brand must be between 1 and 100 characters'
        }
      }
    },
    model: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Model is required'
        },
        len: {
          args: [1, 100],
          msg: 'Model must be between 1 and 100 characters'
        }
      }
    },
    specifications_json: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      validate: {
        isValidJSON(value) {
          if (value && typeof value !== 'object') {
            throw new Error('Specifications must be a valid JSON object');
          }
        }
      }
    },
    base_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: 'Base price must be a valid decimal number'
        },
        min: {
          args: [0.01],
          msg: 'Base price must be positive'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Product',
    tableName: 'products',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['brand']
      },
      {
        fields: ['model']
      },
      {
        fields: ['brand', 'model']
      },
      {
        fields: ['base_price']
      }
    ]
  });

  return Product;
};
