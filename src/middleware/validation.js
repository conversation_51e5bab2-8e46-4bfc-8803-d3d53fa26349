const Joi = require('joi');

/**
 * Generic validation middleware factory
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid input data',
        details: errorDetails,
        timestamp: new Date().toISOString(),
      });
    }

    // Replace the original data with validated and sanitized data
    req[property] = value;
    next();
  };
};

/**
 * User registration validation schema
 */
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.alphanum': 'Username can only contain letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot exceed 50 characters',
      'any.required': 'Username is required',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
      'any.required': 'Email is required',
    }),
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'Password is required',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
});

/**
 * User login validation schema
 */
const loginSchema = Joi.object({
  username: Joi.string()
    .required()
    .messages({
      'any.required': 'Username or email is required',
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
    }),
});

/**
 * Forgot password validation schema
 */
const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'any.required': 'Email is required',
    }),
});

/**
 * Reset password validation schema
 */
const resetPasswordSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': 'Reset token is required',
    }),
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'New password is required',
    }),
});

/**
 * Update user validation schema
 */
const updateUserSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': 'Username can only contain letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot exceed 50 characters',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .optional()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
  is_active: Joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'is_active must be a boolean value',
    }),
});

/**
 * Change password validation schema
 */
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string()
    .required()
    .messages({
      'any.required': 'Current password is required',
    }),
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'New password is required',
    }),
});

/**
 * Query parameters validation schema for user listing
 */
const getUsersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
  is_active: Joi.string()
    .valid('true', 'false')
    .optional()
    .messages({
      'any.only': 'is_active must be true or false',
    }),
  search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Search term cannot exceed 100 characters',
    }),
});

/**
 * UUID parameter validation schema
 */
const uuidParamSchema = Joi.object({
  id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': 'ID must be a valid UUID',
      'any.required': 'ID is required',
    }),
});

/**
 * Integer ID parameter validation schema
 */
const integerIdParamSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID must be a number',
      'number.integer': 'ID must be an integer',
      'number.positive': 'ID must be positive',
      'any.required': 'ID is required',
    }),
});

/**
 * Container creation validation schema
 */
const createContainerSchema = Joi.object({
  container_number: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.alphanum': 'Container number can only contain letters and numbers',
      'string.min': 'Container number must be at least 3 characters long',
      'string.max': 'Container number cannot exceed 50 characters',
      'any.required': 'Container number is required',
    }),
  arrival_date: Joi.date()
    .iso()
    .max('now')
    .required()
    .messages({
      'date.base': 'Arrival date must be a valid date',
      'date.iso': 'Arrival date must be in ISO format',
      'date.max': 'Arrival date cannot be in the future',
      'any.required': 'Arrival date is required',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .default('arrived')
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  total_items: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': 'Total items must be a number',
      'number.integer': 'Total items must be an integer',
      'number.min': 'Total items must be non-negative',
    }),
});

/**
 * Container update validation schema
 */
const updateContainerSchema = Joi.object({
  container_number: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': 'Container number can only contain letters and numbers',
      'string.min': 'Container number must be at least 3 characters long',
      'string.max': 'Container number cannot exceed 50 characters',
    }),
  arrival_date: Joi.date()
    .iso()
    .max('now')
    .optional()
    .messages({
      'date.base': 'Arrival date must be a valid date',
      'date.iso': 'Arrival date must be in ISO format',
      'date.max': 'Arrival date cannot be in the future',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .optional()
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  total_items: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.base': 'Total items must be a number',
      'number.integer': 'Total items must be an integer',
      'number.min': 'Total items must be non-negative',
    }),
});

/**
 * Container query parameters validation schema
 */
const getContainersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .optional()
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  container_number: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': 'Container number cannot exceed 50 characters',
    }),
  date_from: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.base': 'Date from must be a valid date',
      'date.iso': 'Date from must be in ISO format',
    }),
  date_to: Joi.date()
    .iso()
    .optional()
    .when('date_from', {
      is: Joi.exist(),
      then: Joi.date().min(Joi.ref('date_from')),
    })
    .messages({
      'date.base': 'Date to must be a valid date',
      'date.iso': 'Date to must be in ISO format',
      'date.min': 'Date to must be after date from',
    }),
});

/**
 * Bulk import validation schema
 */
const bulkImportSchema = Joi.object({
  items: Joi.array()
    .items(
      Joi.object({
        product_id: Joi.string()
          .uuid()
          .required()
          .messages({
            'string.uuid': 'Product ID must be a valid UUID',
            'any.required': 'Product ID is required',
          }),
        serial_number: Joi.string()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Serial number cannot be empty',
            'string.max': 'Serial number cannot exceed 100 characters',
            'any.required': 'Serial number is required',
          }),
        condition: Joi.string()
          .valid('new', 'refurbished', 'used', 'damaged')
          .default('new')
          .messages({
            'any.only': 'Condition must be one of: new, refurbished, used, damaged',
          }),
        cost_price: Joi.number()
          .positive()
          .precision(2)
          .required()
          .messages({
            'number.base': 'Cost price must be a number',
            'number.positive': 'Cost price must be positive',
            'any.required': 'Cost price is required',
          }),
      })
    )
    .min(1)
    .max(1000)
    .required()
    .messages({
      'array.min': 'At least one item is required',
      'array.max': 'Cannot import more than 1000 items at once',
      'any.required': 'Items array is required',
    }),
});

/**
 * Product creation validation schema
 */
const createProductSchema = Joi.object({
  brand: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Brand cannot be empty',
      'string.max': 'Brand cannot exceed 100 characters',
      'any.required': 'Brand is required',
    }),
  model: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Model cannot be empty',
      'string.max': 'Model cannot exceed 100 characters',
      'any.required': 'Model is required',
    }),
  specifications_json: Joi.object({
    cpu: Joi.string().max(200).optional(),
    ram: Joi.string().max(50).optional(),
    storage: Joi.string().max(100).optional(),
    screen_size: Joi.string().max(50).optional(),
    resolution: Joi.string().max(50).optional(),
    graphics: Joi.string().max(200).optional(),
    operating_system: Joi.string().max(100).optional(),
    weight: Joi.string().max(50).optional(),
    color: Joi.string().max(50).optional(),
    battery_life: Joi.string().max(50).optional(),
    ports: Joi.string().max(200).optional(),
    wireless: Joi.string().max(100).optional(),
    warranty: Joi.string().max(100).optional()
  })
    .pattern(/^custom_/, Joi.string().max(500))
    .optional()
    .messages({
      'object.base': 'Specifications must be a valid object',
    }),
  base_price: Joi.number()
    .positive()
    .precision(2)
    .max(999999.99)
    .required()
    .messages({
      'number.base': 'Base price must be a number',
      'number.positive': 'Base price must be positive',
      'number.max': 'Base price cannot exceed 999,999.99',
      'any.required': 'Base price is required',
    }),
  image_url: Joi.string()
    .uri()
    .max(500)
    .optional()
    .messages({
      'string.uri': 'Image URL must be a valid URL',
      'string.max': 'Image URL cannot exceed 500 characters',
    }),
});

/**
 * Product update validation schema
 */
const updateProductSchema = Joi.object({
  brand: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Brand cannot be empty',
      'string.max': 'Brand cannot exceed 100 characters',
    }),
  model: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Model cannot be empty',
      'string.max': 'Model cannot exceed 100 characters',
    }),
  specifications_json: Joi.object({
    cpu: Joi.string().max(200).optional(),
    ram: Joi.string().max(50).optional(),
    storage: Joi.string().max(100).optional(),
    screen_size: Joi.string().max(50).optional(),
    resolution: Joi.string().max(50).optional(),
    graphics: Joi.string().max(200).optional(),
    operating_system: Joi.string().max(100).optional(),
    weight: Joi.string().max(50).optional(),
    color: Joi.string().max(50).optional(),
    battery_life: Joi.string().max(50).optional(),
    ports: Joi.string().max(200).optional(),
    wireless: Joi.string().max(100).optional(),
    warranty: Joi.string().max(100).optional()
  })
    .pattern(/^custom_/, Joi.string().max(500))
    .optional()
    .messages({
      'object.base': 'Specifications must be a valid object',
    }),
  base_price: Joi.number()
    .positive()
    .precision(2)
    .max(999999.99)
    .optional()
    .messages({
      'number.base': 'Base price must be a number',
      'number.positive': 'Base price must be positive',
      'number.max': 'Base price cannot exceed 999,999.99',
    }),
  image_url: Joi.string()
    .uri()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.uri': 'Image URL must be a valid URL',
      'string.max': 'Image URL cannot exceed 500 characters',
    }),
  price_update_reason: Joi.string()
    .max(200)
    .optional()
    .messages({
      'string.max': 'Price update reason cannot exceed 200 characters',
    }),
});

/**
 * Product query parameters validation schema
 */
const getProductsQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  search: Joi.string()
    .max(200)
    .optional()
    .messages({
      'string.max': 'Search term cannot exceed 200 characters',
    }),
  brand: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Brand filter cannot exceed 100 characters',
    }),
  model: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Model filter cannot exceed 100 characters',
    }),
  min_price: Joi.number()
    .positive()
    .precision(2)
    .optional()
    .messages({
      'number.base': 'Minimum price must be a number',
      'number.positive': 'Minimum price must be positive',
    }),
  max_price: Joi.number()
    .positive()
    .precision(2)
    .optional()
    .when('min_price', {
      is: Joi.exist(),
      then: Joi.number().min(Joi.ref('min_price')),
    })
    .messages({
      'number.base': 'Maximum price must be a number',
      'number.positive': 'Maximum price must be positive',
      'number.min': 'Maximum price must be greater than minimum price',
    }),
  sort_by: Joi.string()
    .valid('brand', 'model', 'base_price', 'created_at', 'updated_at')
    .default('created_at')
    .messages({
      'any.only': 'Sort by must be one of: brand, model, base_price, created_at, updated_at',
    }),
  sort_order: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
    .messages({
      'any.only': 'Sort order must be asc or desc',
    }),
  include_deleted: Joi.string()
    .valid('true', 'false')
    .default('false')
    .messages({
      'any.only': 'Include deleted must be true or false',
    }),
  specifications: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Specifications filter cannot exceed 500 characters',
    }),
});

/**
 * Product bulk import validation schema
 */
const productBulkImportSchema = Joi.object({
  products: Joi.array()
    .items(
      Joi.object({
        brand: Joi.string()
          .trim()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Brand cannot be empty',
            'string.max': 'Brand cannot exceed 100 characters',
            'any.required': 'Brand is required',
          }),
        model: Joi.string()
          .trim()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Model cannot be empty',
            'string.max': 'Model cannot exceed 100 characters',
            'any.required': 'Model is required',
          }),
        specifications_json: Joi.object()
          .optional()
          .messages({
            'object.base': 'Specifications must be a valid object',
          }),
        base_price: Joi.number()
          .positive()
          .precision(2)
          .max(999999.99)
          .required()
          .messages({
            'number.base': 'Base price must be a number',
            'number.positive': 'Base price must be positive',
            'number.max': 'Base price cannot exceed 999,999.99',
            'any.required': 'Base price is required',
          }),
        image_url: Joi.string()
          .uri()
          .max(500)
          .optional()
          .messages({
            'string.uri': 'Image URL must be a valid URL',
            'string.max': 'Image URL cannot exceed 500 characters',
          }),
      })
    )
    .min(1)
    .max(500)
    .required()
    .messages({
      'array.min': 'At least one product is required',
      'array.max': 'Cannot import more than 500 products at once',
      'any.required': 'Products array is required',
    }),
});

// Export validation middleware functions
module.exports = {
  validate,
  validateRegister: validate(registerSchema),
  validateLogin: validate(loginSchema),
  validateForgotPassword: validate(forgotPasswordSchema),
  validateResetPassword: validate(resetPasswordSchema),
  validateUpdateUser: validate(updateUserSchema),
  validateChangePassword: validate(changePasswordSchema),
  validateGetUsersQuery: validate(getUsersQuerySchema, 'query'),
  validateUuidParam: validate(uuidParamSchema, 'params'),
  validateIntegerIdParam: validate(integerIdParamSchema, 'params'),
  validateCreateContainer: validate(createContainerSchema),
  validateUpdateContainer: validate(updateContainerSchema),
  validateGetContainersQuery: validate(getContainersQuerySchema, 'query'),
  validateBulkImport: validate(bulkImportSchema),
  validateCreateProduct: validate(createProductSchema),
  validateUpdateProduct: validate(updateProductSchema),
  validateGetProductsQuery: validate(getProductsQuerySchema, 'query'),
  validateProductBulkImport: validate(productBulkImportSchema),
};
