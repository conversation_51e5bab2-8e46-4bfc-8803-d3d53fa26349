const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test credentials
const adminCredentials = {
  username: 'admin',
  password: 'Admin123!@#'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in as admin...');
    const response = await axios.post(`${BASE_URL}/auth/login`, adminCredentials);
    authToken = response.data.token;
    console.log('✅ Login successful');
    return authToken;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function createProduct(productData) {
  try {
    console.log(`📦 Creating product: ${productData.brand} ${productData.model}...`);
    const response = await axios.post(`${BASE_URL}/products`, productData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Product created successfully:', response.data.product.id);
    return response.data.product;
  } catch (error) {
    console.error('❌ Product creation failed:', error.response?.data || error.message);
    throw error;
  }
}

async function getProducts(params = {}) {
  try {
    console.log('📋 Fetching products...');
    const response = await axios.get(`${BASE_URL}/products`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params
    });
    console.log(`✅ Found ${response.data.products.length} products`);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to fetch products:', error.response?.data || error.message);
    throw error;
  }
}

async function getProductById(id) {
  try {
    console.log(`🔍 Fetching product ${id}...`);
    const response = await axios.get(`${BASE_URL}/products/${id}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Product details retrieved');
    return response.data.product;
  } catch (error) {
    console.error('❌ Failed to fetch product:', error.response?.data || error.message);
    throw error;
  }
}

async function updateProduct(id, updateData) {
  try {
    console.log(`✏️ Updating product ${id}...`);
    const response = await axios.put(`${BASE_URL}/products/${id}`, updateData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Product updated successfully');
    return response.data.product;
  } catch (error) {
    console.error('❌ Product update failed:', error.response?.data || error.message);
    throw error;
  }
}

async function bulkImportProducts(products) {
  try {
    console.log(`📦 Bulk importing ${products.length} products...`);
    const response = await axios.post(`${BASE_URL}/products/bulk-import`, { products }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Bulk import completed:', response.data.results.summary);
    return response.data.results;
  } catch (error) {
    console.error('❌ Bulk import failed:', error.response?.data || error.message);
    throw error;
  }
}

async function getAnalytics() {
  try {
    console.log('📊 Fetching product analytics...');
    const response = await axios.get(`${BASE_URL}/products/analytics`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Analytics retrieved');
    return response.data.analytics;
  } catch (error) {
    console.error('❌ Failed to fetch analytics:', error.response?.data || error.message);
    throw error;
  }
}

async function runTests() {
  try {
    // Login
    await login();

    // Test 1: Create individual products
    console.log('\n🧪 Test 1: Creating individual products');
    const product1 = await createProduct({
      brand: 'Dell',
      model: 'XPS 13',
      specifications_json: {
        cpu: 'Intel Core i7-1165G7',
        ram: '16GB LPDDR4x',
        storage: '512GB SSD',
        screen_size: '13.3 inches',
        resolution: '1920x1200',
        operating_system: 'Windows 11 Pro',
        weight: '2.64 lbs'
      },
      base_price: 1299.99,
      image_url: 'https://example.com/dell-xps13.jpg'
    });

    const product2 = await createProduct({
      brand: 'Apple',
      model: 'MacBook Air M2',
      specifications_json: {
        cpu: 'Apple M2',
        ram: '8GB',
        storage: '256GB SSD',
        screen_size: '13.6 inches',
        resolution: '2560x1664',
        operating_system: 'macOS Ventura',
        weight: '2.7 lbs'
      },
      base_price: 1199.99
    });

    // Test 2: Get all products
    console.log('\n🧪 Test 2: Fetching all products');
    const allProducts = await getProducts();

    // Test 3: Search products
    console.log('\n🧪 Test 3: Searching products');
    const searchResults = await getProducts({ search: 'Dell', limit: 5 });

    // Test 4: Get product by ID
    console.log('\n🧪 Test 4: Getting product details');
    const productDetails = await getProductById(product1.id);

    // Test 5: Update product
    console.log('\n🧪 Test 5: Updating product');
    const updatedProduct = await updateProduct(product1.id, {
      base_price: 1399.99,
      price_update_reason: 'Market price adjustment'
    });

    // Test 6: Bulk import
    console.log('\n🧪 Test 6: Bulk importing products');
    const bulkProducts = [
      {
        brand: 'HP',
        model: 'Pavilion 15',
        specifications_json: {
          cpu: 'AMD Ryzen 5 5500U',
          ram: '8GB DDR4',
          storage: '256GB SSD',
          screen_size: '15.6 inches'
        },
        base_price: 699.99
      },
      {
        brand: 'Lenovo',
        model: 'ThinkPad X1 Carbon',
        specifications_json: {
          cpu: 'Intel Core i7-1165G7',
          ram: '16GB LPDDR4x',
          storage: '1TB SSD',
          screen_size: '14 inches'
        },
        base_price: 1899.99
      }
    ];
    const bulkResults = await bulkImportProducts(bulkProducts);

    // Test 7: Get analytics
    console.log('\n🧪 Test 7: Getting analytics');
    const analytics = await getAnalytics();
    console.log('📊 Analytics Overview:', analytics.overview);
    console.log('📊 Brand Distribution:', analytics.brand_distribution);

    // Test 8: Advanced filtering
    console.log('\n🧪 Test 8: Advanced filtering');
    const filteredProducts = await getProducts({
      min_price: 1000,
      max_price: 1500,
      brand: 'Dell',
      sort_by: 'base_price',
      sort_order: 'asc'
    });

    console.log('\n🎉 All tests completed successfully!');
    console.log(`\n📊 Summary:`);
    console.log(`- Created ${2} individual products`);
    console.log(`- Bulk imported ${bulkResults.summary.successful} products`);
    console.log(`- Total products in system: ${analytics.overview.total_products}`);

  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
  }
}

// Run the tests
runTests();
