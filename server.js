const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { getDatabaseHealth, closeDatabaseConnection } = require('./src/utils/dbConnection');
require('dotenv').config();

const app = express();

// Environment variables with defaults
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: NODE_ENV === 'production' ? process.env.ALLOWED_ORIGINS?.split(',') : true,
  credentials: true,
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// HTTP request logging
app.use(morgan(NODE_ENV === 'production' ? 'combined' : 'dev'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await getDatabaseHealth();

    const healthCheck = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      node_version: process.version,
      memory_usage: process.memoryUsage(),
      pid: process.pid,
      ...dbHealth,
    };

    // If database is not connected, return 503 Service Unavailable
    const httpStatus = dbHealth.database.status === 'connected' ? 200 : 503;
    res.status(httpStatus).json(healthCheck);
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      node_version: process.version,
      memory_usage: process.memoryUsage(),
      pid: process.pid,
      database: {
        status: 'error',
        error: error.message,
        last_check: new Date().toISOString(),
      },
    });
  }
});

// Import routes
const authRoutes = require('./src/routes/auth');
const userRoutes = require('./src/routes/users');
const containerRoutes = require('./src/routes/containers');
const productRoutes = require('./src/routes/products');

// Import request logging middleware
const { authLogger, userLogger, logFailedAuth, logSuccessfulAuth, logPasswordChange } = require('./src/middleware/requestLogger');

// Apply request logging middleware
app.use(authLogger);
app.use(userLogger);
app.use(logFailedAuth);
app.use(logSuccessfulAuth);
app.use(logPasswordChange);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/containers', containerRoutes);
app.use('/api/products', productRoutes);

app.get('/api/health', async (req, res) => {
  try {
    const dbHealth = await getDatabaseHealth();

    const apiHealth = {
      message: 'Bulk Laptop Inventory Management System API',
      status: dbHealth.database.status === 'connected' ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      ...dbHealth,
    };

    // If database is not connected, return 503 Service Unavailable
    const httpStatus = dbHealth.database.status === 'connected' ? 200 : 503;
    res.status(httpStatus).json(apiHealth);
  } catch (error) {
    res.status(503).json({
      message: 'Bulk Laptop Inventory Management System API',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      database: {
        status: 'error',
        error: error.message,
        last_check: new Date().toISOString(),
      },
    });
  }
});

// 404 handler for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString(),
  });
});

// Global error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Default error response
  const errorResponse = {
    error: 'Internal Server Error',
    message: NODE_ENV === 'development' ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString(),
  };
  
  // Include stack trace in development
  if (NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
  }
  
  res.status(err.status || 500).json(errorResponse);
});

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  server.close(async () => {
    await closeDatabaseConnection();
    console.log('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received. Shutting down gracefully...');
  server.close(async () => {
    await closeDatabaseConnection();
    console.log('Process terminated');
    process.exit(0);
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`
🚀 Bulk Laptop Inventory Management System
📡 Server running on port ${PORT}
🌍 Environment: ${NODE_ENV}
⏰ Started at: ${new Date().toISOString()}
🔗 Health check: http://localhost:${PORT}/health
🔗 API Health: http://localhost:${PORT}/api/health
  `);
});

module.exports = app;
